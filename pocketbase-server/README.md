# PocketBase Server

This is the PocketBase server workspace for the urusan-financial application. PocketBase provides a real-time database with built-in authentication, file storage, and admin dashboard.

## Features

- Real-time database with SQLite backend
- Built-in authentication and user management
- File storage and uploads
- Admin dashboard UI
- REST and realtime APIs


## Setup

1. Create admin user (first time only):
```bash
bun run admin
```

2. Start development server:
```bash
bun run dev
```

The server will be available at:
- Admin UI: http://127.0.0.1:8090/_/
- API: http://127.0.0.1:8090/api/

## Scripts

- `bun run dev` - Start PocketBase in development mode with auto-reload
- `bun run start` - Start PocketBase in production mode
- `bun run admin` - Create admin user
- `bun run migrate` - Run database migrations


## Configuration

PocketBase configuration is stored in `pb_data/` directory:
- `pb_data/data.db` - SQLite database
- `pb_data/logs.db` - Application logs
- `pb_data/storage/` - File uploads

## API Endpoints

- Admin UI: http://localhost:8090/_/
- API Base: http://localhost:8090/api/
- Collections: http://localhost:8090/api/collections/
- Auth: http://localhost:8090/api/collections/users/auth-with-password

## Environment Variables

- `PB_PORT` - Server port (default: 8090)
- `PB_HOST` - Server host (default: 127.0.0.1)
- `PB_DATA_DIR` - Data directory (default: ./pb_data)
