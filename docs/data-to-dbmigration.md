# Data Structures for Database Migration

This document outlines all data structures currently stored in the client that need to be migrated to the server database.

## Overview

The client application currently stores all data in `localStorage` under the key `shopping-list-data`. This data needs to be migrated to a server-side database with proper authentication and API endpoints.

## Current Client Data Structure

### 1. Core Entity Types (from `shared/src/types/index.ts`)

#### User
```typescript
export type User = {
  id: string;
  name: string;
  color: string;
  createdAt?: string;
}
```

#### Application
```typescript
export type Application = {
  id: string;
  name: string;
  icon: string;
  isActive: boolean;
}
```

#### Bucket
```typescript
export type Bucket = {
  id: string;
  name: string;
  emoji: string;
  ownerId: string;
  categories?: Category[];
  collaborators?: BucketCollaborator[];
  budget?: number;
  createdAt?: string;
  updatedAt?: string;
}
```

#### BucketCollaborator
```typescript
export type BucketCollaborator = {
  bucketId: string;
  userId: string;
  role: 'owner' | 'member';
  joinedAt?: string;
}
```

#### Category
```typescript
export type Category = {
  id: string;
  name: string;
  bucketId: string;
  items?: Item[];
  createdAt?: string;
  updatedAt?: string;
}
```

#### Item
```typescript
export type Item = {
  id: string;
  name: string;
  notes?: string;
  categoryId: string;
  purchaseOptions?: PurchaseOption[];
  status: ItemStatus;
  assignedUserId?: string;
  selectedOptionId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export type ItemStatus = 'pending' | 'in-cart' | 'bought';
```

#### PurchaseOption
```typescript
export type PurchaseOption = {
  id: string;
  itemId: string;
  storeName: string;
  price?: number;
  link?: string;
  createdAt?: string;
}
```

### 2. Client State Data (from `shopping-context.tsx`)

The client stores the following data structure in localStorage:

```typescript
interface ClientStorageData {
  applications: Application[];
  buckets: Bucket[];
  users: User[];
  activeBucketId: string | null;
  activeCategoryId: string | null;
}
```

### 3. Default Data Currently in Client

#### Applications
```typescript
const defaultApplications = [
  {
    id: "shopping-list",
    name: "Shopping List",
    icon: "🛍️",
    isActive: true,
  },
  {
    id: "budget-planning",
    name: "Budget Planning",
    icon: "💰",
    isActive: false,
  },
  {
    id: "debt-management",
    name: "Debt Management",
    icon: "📊",
    isActive: false,
  },
];
```

#### Users
```typescript
const defaultUsers = [
  {
    id: "user-1",
    name: "Tegar Imansyah",
    color: "#3b82f6",
  },
  {
    id: "user-2",
    name: "Wife",
    color: "#ec4899",
  },
];
```

## Current Database Schema Status

### ✅ Already Implemented Tables

1. **users** - Matches User type
2. **buckets** - Matches Bucket type (missing budget field)
3. **bucket_collaborators** - Matches BucketCollaborator type
4. **categories** - Matches Category type
5. **items** - Matches Item type
6. **purchase_options** - Matches PurchaseOption type
7. **change_log** - For audit trail

### ❌ Missing Tables/Fields

1. **applications** table - Not implemented
2. **user_preferences** table - For storing activeBucketId, activeCategoryId per user
3. **budget** field in buckets table - Missing from current schema

## Required Database Schema Updates

### 1. Add Applications Table
```sql
CREATE TABLE applications (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  icon TEXT NOT NULL,
  is_active BOOLEAN DEFAULT false,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Add User Preferences Table
```sql
CREATE TABLE user_preferences (
  user_id TEXT PRIMARY KEY REFERENCES users(id),
  active_bucket_id TEXT REFERENCES buckets(id),
  active_category_id TEXT REFERENCES categories(id),
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Update Buckets Table
```sql
ALTER TABLE buckets ADD COLUMN budget REAL;
```

## Migration Strategy

### Phase 1: Schema Updates
1. Add missing tables (applications, user_preferences)
2. Add missing fields (budget in buckets)
3. Update Drizzle schema definitions

### Phase 2: Data Migration
1. Migrate default applications data
2. Migrate user data from localStorage
3. Migrate bucket/category/item hierarchies
4. Set user preferences based on localStorage state

### Phase 3: API Development
1. Create CRUD endpoints for applications
2. Create user preferences endpoints
3. Update existing endpoints to handle new fields
4. Implement proper authentication and authorization

## Data Relationships

```
User (1) ←→ (N) UserPreferences
User (1) ←→ (N) Bucket (owner)
User (N) ←→ (N) Bucket (collaborators)
Bucket (1) ←→ (N) Category
Category (1) ←→ (N) Item
Item (1) ←→ (N) PurchaseOption
User (1) ←→ (N) Item (assigned)
```

## Additional Considerations

### Authentication
- Currently no authentication system
- Need to implement user sessions/JWT
- Associate data with authenticated users

### Data Validation
- Implement proper validation for all entities
- Ensure referential integrity
- Handle cascade deletes appropriately

### Performance
- Add database indexes for frequently queried fields
- Implement pagination for large datasets
- Consider caching strategies

### Security
- Implement proper authorization checks
- Ensure users can only access their own data
- Validate collaborator permissions for shared buckets
