{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "src/**/*.json", "tsconfig.json", "tsconfig.*.json", "vite.config.ts", "package.json", "bun.lock", "eslint.config.js", "index.html"], "outputs": ["dist/**", "build/**", ".turbo/**", "pb_data/**"], "env": ["NODE_ENV", "VITE_*"]}, "dev": {"cache": false, "persistent": true, "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "tsconfig.json", "tsconfig.*.json", "vite.config.ts", "package.json"], "env": ["NODE_ENV", "PORT", "VITE_*"]}, "lint": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "eslint.config.js", ".eslintrc*", "package.json", "tsconfig.json", "tsconfig.*.json"], "outputs": [".eslint<PERSON>che"], "env": ["NODE_ENV"]}, "type-check": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts", "tsconfig.json", "tsconfig.*.json", "package.json"], "outputs": ["dist/**/*.d.ts", ".tsbuildinfo"], "env": ["NODE_ENV"]}, "test": {"dependsOn": ["^build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "test/**/*.ts", "test/**/*.tsx", "test/**/*.js", "test/**/*.jsx", "__tests__/**/*.ts", "__tests__/**/*.tsx", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "jest.config.*", "vitest.config.*", "package.json", "tsconfig.json", "tsconfig.*.json"], "outputs": ["coverage/**", ".nyc_output/**", "test-results/**"], "env": ["NODE_ENV", "CI"]}}, "globalDependencies": ["**/.env", "**/.env.*", "**/.env.local", "**/.env.*.local", ".giti<PERSON>re", "turbo.json", "package.json", "bun.lock", "tsconfig.json"], "globalEnv": ["NODE_ENV", "CI", "TURBO_TOKEN", "TURBO_TEAM", "TURBO_REMOTE_ONLY"]}